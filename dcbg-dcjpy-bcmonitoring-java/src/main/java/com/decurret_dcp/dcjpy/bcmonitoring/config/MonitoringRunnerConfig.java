package com.decurret_dcp.dcjpy.bcmonitoring.config;

import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService;
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService;
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService;
import org.springframework.boot.CommandLineRunner;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.support.RetryTemplate;

import java.util.Objects;

@Configuration
public class MonitoringRunnerConfig {

  private final LoggingService logger;
  private final MonitorEventService monitorEventService;
  private final DownloadAbiService downloadAbiService;
  private final RetryTemplate retryTemplate;
  private final BcmonitoringConfigurationProperties properties;
  public MonitoringRunnerConfig(
      LoggingService logger,
      MonitorEventService monitorEventService,
      DownloadAbiService downloadAbiService,
      RetryTemplate retryTemplate,
      BcmonitoringConfigurationProperties properties) {
    this.logger = logger;
    this.monitorEventService = monitorEventService;
    this.downloadAbiService = downloadAbiService;
    this.retryTemplate = retryTemplate;
    this.properties = properties;
  }

  /**
   * This method is used to start the monitoring process. It will run in a virtual thread and will
   * monitor events. If an error occurs, it will restart the monitoring process after a delay.
   */
  @Bean
  public CommandLineRunner commandLineRunner() {
    return args -> {
        if (properties.isEagerStart() || (args != null && args.length > 0 && Objects.equals(args[0], "-f"))) {
            startBCMonitoring();
        }
    };
  }

  private void startBCMonitoring() {
      logger.info("Starting bc monitoring");
      try {
          downloadAbiService.execute();
          // Start monitoring events
          logger.info("Started bc monitoring");
          try {
              retryTemplate.execute(
                      context -> {
                          monitorEventService.execute();
                          logger.info("Monitoring events...");
                          return null;
                      });
          } catch (Exception e) {
              logger.error("Error in bc monitoring after retries", e);
          }
      } catch (Exception e) {
          logger.error("Error starting bc monitoring", e);
      }
  }
}

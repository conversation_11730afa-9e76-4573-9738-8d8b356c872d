package adhoc.startup

import adhoc.helper.AdhocHelper
import ch.qos.logback.classic.Logger
import ch.qos.logback.classic.spi.ILoggingEvent
import ch.qos.logback.core.read.ListAppender
import com.decurret_dcp.dcjpy.bcmonitoring.BcmonitoringApplication
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.S3AbiRepository
import com.decurret_dcp.dcjpy.bcmonitoring.application.abi.DownloadAbiService
import com.decurret_dcp.dcjpy.bcmonitoring.application.event.MonitorEventService
import com.decurret_dcp.dcjpy.bcmonitoring.config.MonitoringRetryListener
import com.decurret_dcp.dcjpy.bcmonitoring.config.Web3jConfig
import com.decurret_dcp.dcjpy.bcmonitoring.logging.LoggingService
import io.reactivex.Flowable
import io.reactivex.processors.PublishProcessor
import org.slf4j.LoggerFactory
import org.springframework.beans.factory.annotation.Autowired
import org.springframework.boot.CommandLineRunner
import org.springframework.boot.test.context.SpringBootTest
import org.springframework.boot.test.mock.mockito.SpyBean
import org.springframework.context.ApplicationContext
import org.springframework.retry.support.RetryTemplate
import org.springframework.test.context.DynamicPropertyRegistry
import org.springframework.test.context.DynamicPropertySource
import org.springframework.test.context.bean.override.mockito.MockitoSpyBean
import org.web3j.protocol.Web3j
import org.web3j.protocol.core.Request
import org.web3j.protocol.core.methods.request.EthFilter
import org.web3j.protocol.core.methods.response.EthBlock
import org.web3j.protocol.core.methods.response.EthLog
import software.amazon.awssdk.auth.credentials.AwsBasicCredentials
import software.amazon.awssdk.auth.credentials.StaticCredentialsProvider
import software.amazon.awssdk.regions.Region
import software.amazon.awssdk.services.dynamodb.DynamoDbClient
import software.amazon.awssdk.services.s3.S3Client
import software.amazon.awssdk.services.s3.model.DeleteObjectRequest
import software.amazon.awssdk.services.s3.model.ListObjectsV2Request
import software.amazon.awssdk.services.s3.model.PutObjectRequest
import spock.lang.Shared
import spock.lang.Specification
import com.decurret_dcp.dcjpy.bcmonitoring.adaptor.infrastructure.s3.AbiParser

import java.net.http.WebSocketHandshakeException
import java.util.concurrent.atomic.AtomicBoolean
import java.util.concurrent.Executors
import java.util.concurrent.TimeUnit
import java.util.concurrent.atomic.AtomicInteger

@SpringBootTest(
        classes = [BcmonitoringApplication.class],
        webEnvironment = SpringBootTest.WebEnvironment.NONE
)
class StartupServiceSpec extends Specification {

    @Shared
    DynamoDbClient dynamoDbClient

    @Shared
    S3Client s3Client

    @Shared
    def logger = LoggerFactory.getLogger(LoggingService.class) as Logger

    @Shared
    def abiParserLogger = LoggerFactory.getLogger(AbiParser.class) as Logger

    @SpyBean
    LoggingService loggingService

    @SpyBean
    MonitoringRetryListener retryListener

    @Autowired
    DownloadAbiService downloadAbiService

    @Autowired
    MonitorEventService monitorEventService

    @Autowired
    S3AbiRepository s3AbiRepository

    @Autowired
    ApplicationContext applicationContext

    @Autowired
    RetryTemplate retryTemplate

    @MockitoSpyBean
    Web3jConfig web3jConfig

    static final String TEST_BUCKET = "abijson-local-bucket"  // Same as default in application.properties
    static final String EVENTS_TABLE = "local-Events"        // Same as default in application.properties
    static final String BLOCK_HEIGHT_TABLE = "local-BlockHeight"  // Same as default in application.properties

    def logAppender = new ListAppender<ILoggingEvent>()
    def web3j = Mock(Web3j)
    def scheduler = Executors.newScheduledThreadPool(1);


    @DynamicPropertySource
    static void configureProperties(DynamicPropertyRegistry registry) {
        registry.add("local-stack.end-point", { "http://localhost:" + AdhocHelper.getLocalStackPort() })
        registry.add("eagerStart", () -> "false")
        registry.add("aws.dynamodb.table-prefix", () -> "")  // No prefix in tests
    }

    def setupSpec() {
        // Create DynamoDB client for LocalStack
        dynamoDbClient = DynamoDbClient.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("access123", "secret123")))
                .region(Region.AP_NORTHEAST_1)
                .build()

        // Create S3 client for LocalStack
        s3Client = S3Client.builder()
                .endpointOverride(URI.create("http://localhost:" + AdhocHelper.getLocalStackPort()))
                .credentialsProvider(StaticCredentialsProvider.create(
                        AwsBasicCredentials.create("access123", "secret123")))
                .region(Region.AP_NORTHEAST_1)
                .forcePathStyle(true)
                .build()

        // Create tables and bucket
        AdhocHelper.createEventsTable(dynamoDbClient, EVENTS_TABLE)
        AdhocHelper.createBlockHeightTable(dynamoDbClient, BLOCK_HEIGHT_TABLE)
        AdhocHelper.createS3Bucket(s3Client, TEST_BUCKET)
    }

    def cleanupSpec() {
        dynamoDbClient?.close()
        s3Client?.close()
    }

    def setup() {
        // Clear all S3 bucket contents completely
        clearS3BucketCompletely()
        // Upload real ABI files to
        AdhocHelper.uploadRealAbiFiles(s3Client, TEST_BUCKET, "3000", [
                "Token",
                "Account",
                "Provider"
        ])
        // Clear all DynamoDB table contents
        clearDynamoDBTables()
        // Setup web3j mock
        setupWeb3jMock()
        // Start log appender to capture logs
        logAppender.start()
        logger.addAppender(logAppender)
        abiParserLogger.addAppender(logAppender)
    }

    private void setupWeb3jMock() {

        def field = Web3jConfig.class.getDeclaredField("web3j")
        field.setAccessible(true)
        field.set(web3jConfig, web3j)
        println("Web3j mock setup completed")
    }

    private void setUpEventStream(List<EthBlock> blocks) {
        def processor = PublishProcessor.<EthBlock> create()
        def index = new AtomicInteger(0)
        scheduler.scheduleAtFixedRate(() -> {
            int i = index.getAndIncrement()
            if (i < blocks.size()) {
                processor.onNext(blocks.get(i))
            } else {
                processor.onComplete()
            }
        }, 0, 2, TimeUnit.SECONDS)

        web3j.blockFlowable(false) >> Flowable.fromPublisher(processor)
    }

    private void setUpPendingEvent(List<EthLog.LogResult> resultList) {
        def mockRequest = Mock(Request)
        def mockLog = Mock(EthLog)

        web3j.ethGetLogs(_ as EthFilter) >> mockRequest
        mockRequest.send() >> mockLog
        mockLog.getLogs() >> resultList
    }

    def cleanup() {
        // Clear S3 bucket for next test
        clearS3Bucket()
        // Shut down the scheduler to stop mock event generation
        scheduler.shutdown()
        scheduler.awaitTermination(5, TimeUnit.SECONDS)
    }

    private void clearS3Bucket() {
        clearS3BucketCompletely()
    }

    private void clearS3BucketCompletely() {
        try {
            // List all objects including versions and delete markers
            def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
                    .bucket(TEST_BUCKET)
                    .build())

            // Delete all objects
            listResponse.contents().each { obj ->
                s3Client.deleteObject(DeleteObjectRequest.builder()
                        .bucket(TEST_BUCKET)
                        .key(obj.key())
                        .build())
            }

            // Verify bucket is empty
            def verifyResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
                    .bucket(TEST_BUCKET)
                    .build())

            if (verifyResponse.contents().isEmpty()) {
                println("S3 bucket ${TEST_BUCKET} successfully cleared")
            } else {
                println("Warning: S3 bucket ${TEST_BUCKET} still contains ${verifyResponse.contents().size()} objects")
            }
        } catch (Exception e) {
            println("Error clearing S3 bucket: ${e.message}")
            e.printStackTrace()
        }
    }

    private void clearDynamoDBTables() {
        try {
            // Clear events table
            clearDynamoDBTable(EVENTS_TABLE, ["transactionHash", "logIndex"])

            // Clear block height table
            clearDynamoDBTable(BLOCK_HEIGHT_TABLE, ["id"])
        } catch (Exception e) {
            println("Error clearing DynamoDB tables: ${e.message}")
            e.printStackTrace()
        }
    }

    private void clearDynamoDBTable(String tableName, List<String> keyAttributes) {
        try {
            // Scan the table to get all items
            def scanRequest = software.amazon.awssdk.services.dynamodb.model.ScanRequest.builder()
                    .tableName(tableName)
                    .build()

            def scanResponse = dynamoDbClient.scan(scanRequest)

            // Delete each item
            scanResponse.items().each { item ->
                def keyMap = [:]
                keyAttributes.each { keyAttr ->
                    if (item.containsKey(keyAttr)) {
                        keyMap[keyAttr] = item[keyAttr]
                    }
                }

                if (!keyMap.isEmpty()) {
                    def deleteRequest = software.amazon.awssdk.services.dynamodb.model.DeleteItemRequest.builder()
                            .tableName(tableName)
                            .key(keyMap)
                            .build()
                    dynamoDbClient.deleteItem(deleteRequest)
                }
            }
        } catch (Exception e) {
            println("Error clearing DynamoDB table ${tableName}: ${e.message}")
        }
    }

    private void createAbiFile(String key, String content) {
        println("Creating ABI file: ${key}")
        s3Client.putObject(PutObjectRequest.builder()
                .bucket(TEST_BUCKET)
                .key(key)
                .build(),
                software.amazon.awssdk.core.sync.RequestBody.fromString(content))
    }

    /**
     * Successful Service Startup
     * Verifies service starts successfully with all dependencies available
     * Expected: Service logs "Starting bc monitoring" and "Started bc monitoring"
     */
    def "Should start successfully with all dependencies available"() {
        given: "Valid environment with accessible dependencies"
        setUpEventStream(Collections.emptyList())
        setUpPendingEvent(Collections.emptyList())
        when: "Testing real service startup with CommandLineRunner"
        // Get the CommandLineRunner bean and execute it to trigger the logs
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        scheduler.schedule(() -> {
            stopBCMonitoring()
        }, 10, TimeUnit.SECONDS)

        commandLineRunner.run("-f")

        then: "Service should start successfully and log the required messages"
        noExceptionThrown()
        logAppender.list*.formattedMessage.contains("Started bc monitoring")
        logAppender.list*.formattedMessage.contains("ABI file processed:")
        and: "Real services should be available"
        downloadAbiService != null
        monitorEventService != null
        loggingService != null

        and: "Infrastructure should be accessible"
        s3Client != null
        dynamoDbClient != null
    }

    /**
     * Service Restart After WebSocket Error
     * Verifies service automatically restarts monitoring after WebSocket handshake error
     * Expected: Service retries 5 times with WebSocketHandshakeException, logs "restart bc monitoring" 5 times
     */
    def "Should automatically restart monitoring after WebSocket handshake error"() {
        given: "WebSocket handshake error scenario that will trigger retry mechanism"
        // Set up pending event mock like the working test case
        setUpPendingEvent(Collections.emptyList())

        // Mock Web3j to throw WebSocketHandshakeException on blockFlowable call
        web3j.blockFlowable(false) >> { throw new WebSocketHandshakeException("WebSocket handshake failed") }

        when: "Testing real service startup with CommandLineRunner that will trigger retries"
        // Get the CommandLineRunner bean and execute it to trigger the logs
        def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)

        // Schedule to stop the service after retries complete
        scheduler.schedule(() -> {
            stopBCMonitoring()
        }, 20, TimeUnit.SECONDS)  // Give enough time for 5 retries with 3s backoff each

        commandLineRunner.run("-f")

        then: "Service should retry and log restart messages"
        noExceptionThrown()

        and: "Should log 'restart bc monitoring' for each retry attempt (5 times)"
        def restartLogs = logAppender.list.findAll { it.formattedMessage.contains("restart bc monitoring") }
        restartLogs.size() == 5

        and: "Should log retry attempt details"
        def retryAttemptLogs = logAppender.list.findAll { it.formattedMessage.contains("Retry attempt") && it.formattedMessage.contains("failed") }
        retryAttemptLogs.size() == 5

        and: "Real services should be available"
        downloadAbiService != null
        monitorEventService != null
        loggingService != null

        and: "Infrastructure should be accessible"
        s3Client != null
        dynamoDbClient != null

        and: "Retry configuration should be properly set up"
        retryListener != null
        retryTemplate != null
    }

    /**
     * Service Startup with Empty ABI Bucket
     * Verifies service starts successfully when S3 bucket exists but contains no ABI files
     * Expected: Service starts with no contract addresses loaded, application continues running and logs "Started bc monitoring"
     */
    // def "Should start successfully with empty ABI bucket"() {
    //     given: "Empty S3 bucket with valid other dependencies"
    //     // S3 bucket is already cleared in setup() method, so it's empty
    //     // This simulates the scenario where S3 is accessible but has no ABI files

    //     when: "Testing real service startup with empty bucket"
    //     // Get the CommandLineRunner bean and execute it to trigger the logs
    //     def commandLineRunner = applicationContext.getBean(CommandLineRunner.class)
    //     commandLineRunner.run()

    //     then: "Service should start successfully even with empty bucket"
    //     noExceptionThrown()

    //     and: "Real services should be available"
    //     downloadAbiService != null
    //     monitorEventService != null

    //     and: "S3 bucket should be accessible but empty"
    //     s3Client != null
    //     def listResponse = s3Client.listObjectsV2(ListObjectsV2Request.builder()
    //             .bucket(TEST_BUCKET)
    //             .build())
    //     listResponse.contents().isEmpty()

    //     and: "DynamoDB should be accessible"
    //     dynamoDbClient != null

    //     and: "MonitorEventService should be ready to start monitoring (with no contracts)"
    //     // Even with no ABI files, the service should be ready to monitor
    //     monitorEventService.class.name.contains("MonitorEventService")
    // }

    private void stopBCMonitoring() {
        def field = MonitorEventService.class.getDeclaredField("running")
        field.setAccessible(true)
        AtomicBoolean running = field.get(monitorEventService) as AtomicBoolean
        running.set(false)
    }
}